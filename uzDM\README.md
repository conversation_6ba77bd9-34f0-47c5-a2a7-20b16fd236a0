# DanMu4.js 测试脚本

基于 `danMu4.js` 弹幕解析扩展的完整流程测试脚本，涵盖从输入到搜索、匹配、解析、返回输出的所有步骤。

## 功能特性

### 🔍 完整流程测试
- **输入验证** - 验证视频名称、集数等参数
- **视频搜索** - 通过多个采集站搜索视频播放地址
- **弹幕匹配** - 从多个弹幕源并发获取弹幕数据
- **数据解析** - 支持XML和JSON两种弹幕格式解析
- **内容过滤** - 过滤广告、垃圾弹幕等无效内容
- **结果输出** - 返回处理后的结构化弹幕数据

### 🛣️ 弹幕解析线路
- **智能模式** - 测试所有线路，返回弹幕数最多的结果
- **快速模式** - 返回第一个有效结果，提高响应速度
- **指定线路** - 支持指定特定的弹幕解析线路

### 📡 支持的弹幕源
- 虾米弹幕 (dmku.hls.one)
- DouFun弹幕 (danmu.56uxi.com)
- 弹幕库 (api.danmu.icu)
- 678弹幕 (se.678.ooo)

### 🎬 支持的采集站
- 小猫咪 (zy.xmm.hk)
- 舒渡 (www.69mu.cn)
- 听风 (gctf.tfdh.top)

## 安装依赖

```bash
npm install
# 或者
npm run install-deps
```

## 使用方法

### 直接运行测试
```bash
npm test
# 或者
node danmu_test.js
```

### 作为模块使用
```javascript
const { searchDanMu, SearchParameters } = require('./danmu_test');

async function example() {
    const args = new SearchParameters();
    args.name = '鬼灭之刃';
    args.episode = '1';
    args.line = '智能';
    
    const result = await searchDanMu(args);
    console.log('弹幕数量:', result.data.length);
    console.log('错误信息:', result.error);
}
```

## 测试用例

脚本内置了4个测试用例：

1. **热门动漫测试** - 测试《鬼灭之刃》第1集，使用智能模式
2. **电视剧测试** - 测试《庆余年》第1集，使用快速模式
3. **指定线路测试** - 测试《斗罗大陆》第1集，指定虾米线路
4. **直接URL测试** - 测试直接提供视频URL的情况

## 输出示例

```
🧪 DanMu4.js 完整流程测试开始
================================================================================

🔬 测试案例1: 热门动漫
==================================================

🚀 开始弹幕搜索流程
📋 输入参数: {
  "name": "鬼灭之刃",
  "episode": "1",
  "line": "智能"
}

📝 步骤1: 参数验证
✅ 参数验证通过

⚙️  步骤2: 初始化配置
✅ 屏蔽词正则表达式初始化完成

🎬 步骤3: 获取视频播放地址
🔍 开始搜索视频: 鬼灭之刃 第1集
   📡 搜索采集站 小猫咪: https://zy.xmm.hk/api.php/provide/vod/?ac=detail&wd=鬼灭之刃&pg=1
   ✅ 小猫咪 找到 1 个结果
   🎯 匹配到视频: 鬼灭之刃
   🎬 找到集数: 第01集 -> https://example.com/video1.m3u8

✅ 视频地址获取成功:
   📺 视频名称: 鬼灭之刃
   📺 集数: 第01集
   📺 采集站: 小猫咪
   🔗 播放地址: https://example.com/video1.m3u8

🛣️  步骤4: 确定弹幕解析线路
🧠 使用智能模式
📡 将测试 4 个弹幕解析线路: [ '虾米', 'DouFun', '弹幕库', '678' ]

🔄 步骤5: 并发请求弹幕数据
   📡 请求 虾米: https://dmku.hls.one/?ac=dm&url=https%3A//example.com/video1.m3u8
   📥 虾米 返回数据长度: 15420 字符
   ✅ 虾米 解析到 156 条弹幕
   📡 请求 DouFun: https://danmu.56uxi.com/?ac=dm&url=https%3A//example.com/video1.m3u8
   📥 DouFun 返回数据长度: 12380 字符
   ✅ DouFun 解析到 142 条弹幕

📊 步骤6: 处理弹幕解析结果
🧠 智能模式: 等待所有结果并选择最佳
📈 各线路弹幕数量统计:
   虾米: 156 条
   DouFun: 142 条

🎉 步骤7: 生成最终结果
✅ 弹幕解析成功!
   🏆 最佳线路: 虾米
   📊 弹幕数量: 156 条
   📝 弹幕示例:
      1. [12.5s] 开始了开始了
      2. [15.2s] 炭治郎好帅
      3. [18.7s] 这画质绝了
      4. [22.1s] 鬼灭之刃YYDS
      5. [25.8s] 期待后续剧情

⏱️  执行时间: 3245ms
📋 最终结果:
   状态: ✅ 成功
   弹幕数量: 156 条
```

## API 文档

### SearchParameters 类
```javascript
class SearchParameters {
    name: string;      // 视频名称（必需）
    episode: string;   // 集数（必需）
    videoUrl: string;  // 直接视频URL（可选）
    line: string;      // 弹幕线路（可选：智能/快速/虾米/DouFun/弹幕库/678）
}
```

### BackData 类
```javascript
class BackData {
    data: DanMu[];     // 弹幕数据数组
    error: string;     // 错误信息
}
```

### DanMu 类
```javascript
class DanMu {
    content: string;   // 弹幕内容
    time: number;      // 出现时间（秒）
}
```

## 注意事项

1. **网络依赖** - 测试需要访问真实的采集站和弹幕API，请确保网络连接正常
2. **请求频率** - 脚本在测试用例间添加了2秒延迟，避免请求过于频繁
3. **超时设置** - 单个请求超时时间为20秒，可根据网络情况调整
4. **错误处理** - 脚本包含完整的错误处理和日志输出，便于调试

## 许可证

MIT License
