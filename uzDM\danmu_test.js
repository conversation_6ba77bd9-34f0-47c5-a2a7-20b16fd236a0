#!/usr/bin/env node

/**
 * DanMu4.js 完整测试脚本
 * 测试从输入到搜索、匹配、解析、返回输出的完整流程
 */

const axios = require('axios');
const cheerio = require('cheerio');
const readline = require('readline');

// ==================== 常量定义 ====================
const REQUEST_TIMEOUT_MS = 20000;
const DANMU_DATA_SKIP_COUNT = 2;

// 分隔符常量
const LINE_SEPARATOR = '|';
const NAME_URL_SEPARATOR = '@';
const PLAY_URL_SEPARATOR = '$$$';
const EPISODE_SEPARATOR = '#';
const NAME_URL_PAIR_SEPARATOR = '$';

// 正则表达式
const SPECIAL_CONTENT_REGEX = /预告|花絮|预览|片花/;
const PLACEHOLDER_DANMU_REGEX = /有\d+条弹幕|弹幕库世界|剧圈|juo\.one/i;

// 默认配置
const DEFAULT_BLOCK_WORDS = [
    'http', 'https', 'www', 'com', 'xyz', 'cn', 'net', 'org',
    '加群', '微信', 'QQ群', '关注', '订阅', '点赞', '投币', '收藏', '分享',
    '免费', '资源', '下载', '官网', 'APP'
];

const DEFAULT_CAIJI_SITES = [
    { name: '小猫咪', url: 'https://zy.xmm.hk/api.php/provide/vod' },
    { name: '舒渡', url: 'https://www.69mu.cn/api.php/provide/vod' },
    { name: '听风', url: 'https://gctf.tfdh.top/api.php/provide/vod' }
];

const DEFAULT_DANMU_LINES = [
    { name: '虾米', url: 'https://dmku.hls.one/?ac=dm&url=' },
    { name: 'DouFun', url: 'https://danmu.56uxi.com/?ac=dm&url=' },
    { name: '弹幕库', url: 'https://api.danmu.icu/?ac=dm&url=' },
    { name: '678', url: 'https://se.678.ooo/?ac=dm&url=' }
];

// ==================== 数据类型定义 ====================
class DanMu {
    constructor() {
        this.content = '';
        this.time = 0;
    }
}

class BackData {
    constructor() {
        this.data = [];
        this.error = '';
    }
}

class SearchParameters {
    constructor() {
        this.name = '';
        this.episode = '';
        this.videoUrl = '';
        this.line = '';
    }
}

// ==================== 全局变量 ====================
let BLOCK_LIST_REGEX = null;

// ==================== 工具函数 ====================
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function parseCompatibleSeparator(str) {
    if (!str || typeof str !== 'string') {
        return [];
    }
    
    if (str.includes(LINE_SEPARATOR)) {
        return str.split(LINE_SEPARATOR);
    } else if (str.includes(';')) {
        return str.split(';');
    } else {
        return [str];
    }
}

function validateSearchArgs(args) {
    if (!args || typeof args !== 'object') {
        return '搜索参数无效';
    }
    
    if (!args.name || typeof args.name !== 'string') {
        return '请提供有效的视频名称';
    }
    
    if (args.episode == null || args.episode === '' ||
        (typeof args.episode !== 'string' && typeof args.episode !== 'number')) {
        return '请提供有效的集数';
    }
    
    return null;
}

// 初始化屏蔽词正则表达式
function initBlockWordsRegex() {
    const finalBlockWords = DEFAULT_BLOCK_WORDS;
    
    if (finalBlockWords.length > 0) {
        BLOCK_LIST_REGEX = new RegExp(finalBlockWords.map(escapeRegExp).join('|'), 'i');
    } else {
        BLOCK_LIST_REGEX = /(?!.*)/;
    }
}

// 弹幕内容过滤函数
function filterDanmuContent(content) {
    if (typeof content !== 'string') {
        return false;
    }
    
    return !BLOCK_LIST_REGEX.test(content) && !PLACEHOLDER_DANMU_REGEX.test(content);
}

// 解析弹幕数据
function parseDanmuData(data) {
    let danmuList = [];

    // 支持字符串和对象两种数据类型
    let dataStr = '';
    if (typeof data === 'string') {
        dataStr = data.trim();
    } else if (typeof data === 'object' && data !== null) {
        dataStr = JSON.stringify(data);
    } else {
        console.log('🔍 弹幕数据类型不支持:', typeof data);
        return danmuList;
    }

    if (!dataStr) {
        console.log('🔍 弹幕数据为空');
        return danmuList;
    }

    // console.log('🔍 开始解析弹幕数据，数据长度:', dataStr.length);
    // console.log('🔍 数据前100字符:', dataStr.substring(0, 100));

    try {
        // 尝试解析 XML 格式弹幕
        if (dataStr.includes('<d p=')) {
            console.log('       📋 检测到XML格式弹幕');
            const $ = cheerio.load(dataStr, { xmlMode: true });
            let xmlDanmuCount = 0;
            $('d').each((_, element) => {
                const p = $(element).attr('p');
                if (p) {
                    const parts = p.split(',');
                    if (parts.length >= 1) {
                        const danmu = new DanMu();
                        danmu.time = parseFloat(parts[0]) || 0;
                        danmu.content = $(element).text().trim();

                        if (danmu.content && filterDanmuContent(danmu.content)) {
                            danmuList.push(danmu);
                        }
                        xmlDanmuCount++;
                    }
                }
            });
            console.log(`       📊 XML解析: 原始${xmlDanmuCount}条 → 过滤后${danmuList.length}条`);
        }
        // 尝试解析 JSON 格式弹幕
        else {
            console.log('       📋 检测到JSON格式弹幕');
            let jsonData;
            try {
                jsonData = JSON.parse(dataStr);
            } catch (jsonError) {
                console.log('       ❌ JSON解析失败:', jsonError.message);
                return danmuList;
            }

            let danmuArray = jsonData?.danmuku ?? [];
            console.log(`       📊 弹幕数组长度: ${danmuArray.length}`);

            let single = danmuArray.slice(DANMU_DATA_SKIP_COUNT);

            const mappedDanmu = single.map((item) => {
                if (!Array.isArray(item) || item.length < 5) {
                    return null;
                }

                // 处理时间值，可能是null、数字或字符串
                let time = 0;
                if (item[0] !== null && item[0] !== undefined) {
                    const parsedTime = parseFloat(item[0]);
                    if (!isNaN(parsedTime)) {
                        time = parsedTime;
                    }
                }

                const content = String(item[4] || '').trim();

                if (!content) {
                    return null;
                }

                let danMu = new DanMu();
                danMu.content = content;
                danMu.time = time;
                return danMu;
            }).filter(Boolean);

            single = mappedDanmu.filter((danMu) => filterDanmuContent(danMu.content));
            console.log(`       📊 JSON解析: 原始${danmuArray.length}条 → 跳过${DANMU_DATA_SKIP_COUNT}条 → 有效${mappedDanmu.length}条 → 过滤后${single.length}条`);

            danmuList = danmuList.concat(single);
        }
    } catch (error) {
        console.log('       ❌ 解析异常:', error.toString());
    }

    return danmuList;
}

// HTTP请求函数
async function makeRequest(url, timeout = REQUEST_TIMEOUT_MS) {
    try {
        const response = await axios.get(url, {
            timeout: timeout,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        return { data: response.data, error: null };
    } catch (error) {
        return { data: null, error: error.message };
    }
}

// 获取视频播放地址
async function getVideoUrl(args) {
    const validationError = validateSearchArgs(args);
    if (validationError) {
        return { url: '', error: validationError };
    }
    
    // 如果直接提供了视频URL，直接返回
    if (args.videoUrl && typeof args.videoUrl === 'string' && args.videoUrl.startsWith('http')) {
        return { url: args.videoUrl.trim() };
    }
    
    let videoName = args.name;
    let episode = args.episode;
    
    console.log(`🔍 开始搜索视频: ${videoName} 第${episode}集`);
    
    // 并发请求所有采集站
    const promises = DEFAULT_CAIJI_SITES.map(async (siteInfo) => {
        try {
            const searchUrl = `${siteInfo.url}/?ac=detail&wd=${encodeURIComponent(videoName)}&pg=1`;
            console.log(`   📡 搜索采集站 ${siteInfo.name}: ${searchUrl}`);
            
            const result = await makeRequest(searchUrl);
            if (result.error) {
                console.log(`   ❌ ${siteInfo.name} 请求失败: ${result.error}`);
                return { url: '', error: result.error };
            }
            
            if (result.data) {
                // 修复JSON解析问题：检查数据类型
                let data;
                if (typeof result.data === 'string') {
                    try {
                        data = JSON.parse(result.data);
                    } catch (parseError) {
                        console.log(`   ❌ ${siteInfo.name} JSON解析失败: ${parseError.message}`);
                        console.log(`   📄 原始数据: ${result.data.substring(0, 200)}...`);
                        return { url: '', error: `JSON解析失败: ${parseError.message}` };
                    }
                } else if (typeof result.data === 'object') {
                    data = result.data;
                } else {
                    console.log(`   ❌ ${siteInfo.name} 数据类型异常: ${typeof result.data}`);
                    return { url: '', error: `数据类型异常: ${typeof result.data}` };
                }
                if (data.list && data.list.length > 0) {
                    console.log(`   ✅ ${siteInfo.name} 找到 ${data.list.length} 个结果`);
                    
                    // 遍历所有搜索结果，找到名称匹配的视频
                    const video = data.list.find(v => v.vod_name === videoName);
                    if (video && video.vod_play_url) {
                        console.log(`   🎯 匹配到视频: ${video.vod_name}`);
                        
                        const lines = video.vod_play_url.split(PLAY_URL_SEPARATOR);
                        const targetEpisode = episode.toString();
                        
                        // 收集所有匹配的集数URL
                        const matchedUrls = [];
                        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                            const element = lines[lineIndex];
                            const eps = element.split(EPISODE_SEPARATOR);
                            for (let epIndex = 0; epIndex < eps.length; epIndex++) {
                                const ep = eps[epIndex];
                                const epArr = ep.split(NAME_URL_PAIR_SEPARATOR);
                                if (epArr.length === 2) {
                                    const epName = epArr[0];
                                    const epNameTrim = epName.trim();

                                    const episodePatterns = [
                                        `第${targetEpisode}集`,
                                        `${targetEpisode}集`,
                                        targetEpisode
                                    ];

                                    if (episodePatterns.includes(epNameTrim)) {
                                        matchedUrls.push({
                                            url: epArr[1],
                                            realName: video.vod_name,
                                            realEpisode: epName,
                                            caijiSite: siteInfo.name
                                        });
                                    }
                                }
                            }
                        }

                        // 如果找到匹配的集数，返回最后一个（通常是正片，前面可能是预告）
                        if (matchedUrls.length > 0) {
                            const selectedUrl = matchedUrls[matchedUrls.length - 1];
                            console.log(`   🎬 找到集数: ${selectedUrl.realEpisode} -> ${selectedUrl.url}`);
                            if (matchedUrls.length > 1) {
                                console.log(`   📝 注意: 发现${matchedUrls.length}个匹配的集数，选择最后一个（正片）`);
                            }
                            return selectedUrl;
                        }
                        
                        // 回退到索引匹配
                        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                            const element = lines[lineIndex];
                            const eps = element.split(EPISODE_SEPARATOR);
                            for (let epIndex = 0; epIndex < eps.length; epIndex++) {
                                const ep = eps[epIndex];
                                const epArr = ep.split(NAME_URL_PAIR_SEPARATOR);
                                if (epArr.length === 2) {
                                    const epName = epArr[0];
                                    const epNameTrim = epName.trim();
                                    
                                    const episodeNum = parseInt(episode, 10);
                                    if (isNaN(episodeNum)) continue;
                                    
                                    const indexMatches = [
                                        epIndex === episodeNum - 1,
                                        epIndex === episodeNum,
                                        epIndex === episodeNum + 1
                                    ];
                                    
                                    if (indexMatches.some(match => match) &&
                                        !SPECIAL_CONTENT_REGEX.test(epNameTrim)) {
                                        console.log(`   🎬 通过索引找到集数: ${epName} -> ${epArr[1]}`);
                                        return {
                                            url: epArr[1],
                                            realName: video.vod_name,
                                            realEpisode: epName,
                                            caijiSite: siteInfo.name
                                        };
                                    }
                                }
                            }
                        }
                    }
                } else {
                    console.log(`   ⚠️  ${siteInfo.name} 未找到匹配结果`);
                }
            }
        } catch (error) {
            console.log(`   ❌ ${siteInfo.name} 请求异常: ${error.message}`);
        }
        return { url: '', error: '未找到匹配视频' };
    });
    
    // 等待第一个成功的结果
    try {
        const results = await Promise.allSettled(promises);
        const validResults = results
            .filter(result => result.status === 'fulfilled' && result.value.url && result.value.url.length > 0)
            .map(result => result.value);
        
        return validResults.length > 0 ? validResults[0] : { url: '', error: '所有采集站都未找到匹配视频' };
    } catch (error) {
        console.log('获取视频地址失败:', error.toString());
        return { url: '', error: error.toString() };
    }
}

// 搜索弹幕主函数
async function searchDanMu(args) {
    let backData = new BackData();

    console.log('\n🚀 开始弹幕搜索流程');
    console.log('📋 输入参数:', JSON.stringify(args, null, 2));

    // 1. 参数验证
    console.log('\n📝 步骤1: 参数验证');
    const validationError = validateSearchArgs(args);
    if (validationError) {
        console.log('❌ 参数验证失败:', validationError);
        backData.error = validationError;
        return backData;
    }
    console.log('✅ 参数验证通过');

    try {
        // 2. 初始化配置
        console.log('\n⚙️  步骤2: 初始化配置');
        initBlockWordsRegex();
        console.log('✅ 屏蔽词正则表达式初始化完成');

        // 3. 获取视频播放地址
        console.log('\n🎬 步骤3: 获取视频播放地址');
        const videoResult = await getVideoUrl(args);

        if (!videoResult.url || videoResult.url.length === 0) {
            console.log('❌ 未找到视频播放地址');
            backData.error = videoResult.error || '未找到视频播放地址';
            return backData;
        }

        console.log('✅ 视频地址获取成功:');
        console.log(`   📺 视频名称: ${videoResult.realName || args.name}`);
        console.log(`   📺 集数: ${videoResult.realEpisode || args.episode}`);
        console.log(`   📺 采集站: ${videoResult.caijiSite || '未知'}`);
        console.log(`   🔗 播放地址: ${videoResult.url}`);

        const videoUrl = videoResult.url;

        // 4. 确定弹幕解析线路
        console.log('\n🛣️  步骤4: 确定弹幕解析线路');
        let selectedLineName = '智能';
        let lines;
        let useFastMode = false;

        const danmuLines = [
            { name: '智能', url: '' },
            { name: '快速', url: '' },
            ...DEFAULT_DANMU_LINES
        ];

        if (args.line && args.line !== '智能' && args.line !== '快速') {
            const matchedLine = danmuLines.find(line => line.name === args.line);
            if (matchedLine) {
                lines = [{ name: matchedLine.name, url: matchedLine.url }];
                selectedLineName = matchedLine.name;
                console.log(`🎯 使用指定线路: ${selectedLineName}`);
            } else {
                lines = danmuLines.filter(item => item.url.length > 0);
                console.log('⚠️  指定线路不存在，回退到智能模式');
            }
        } else if (args.line === '快速') {
            lines = danmuLines.filter(item => item.url.length > 0);
            selectedLineName = '快速';
            useFastMode = true;
            console.log('⚡ 使用快速模式');
        } else {
            lines = danmuLines.filter(item => item.url.length > 0);
            console.log('🧠 使用智能模式');
        }

        console.log(`📡 将测试 ${lines.length} 个弹幕解析线路:`, lines.map(l => l.name));

        // 5. 并发请求弹幕数据
        console.log('\n🔄 步骤5: 并发请求弹幕数据');
        const promises = lines.map(async (lineInfo) => {
            console.log(`\n   🔄 开始处理 ${lineInfo.name} 线路`);
            console.log(`   ${'─'.repeat(30)}`);
            try {
                let reqUrl = lineInfo.url + encodeURIComponent(videoUrl);
                console.log(`   📡 请求地址: ${reqUrl}`);

                const result = await makeRequest(reqUrl);
                if (result.error) {
                    console.log(`   ❌ 请求失败: ${result.error}`);
                    console.log(`   ${'─'.repeat(30)}`);
                    return null;
                }

                if (result.data) {
                    const dataType = typeof result.data;
                    const dataLength = dataType === 'string' ? result.data.length : JSON.stringify(result.data).length;
                    console.log(`   📥 返回数据类型: ${dataType}, 长度: ${dataLength} 字符`);

                    // 显示前150个字符的数据内容用于调试
                    const dataPreview = dataType === 'string' ? result.data.substring(0, 150) : JSON.stringify(result.data).substring(0, 150);
                    console.log(`   📄 数据预览: ${dataPreview}...`);

                    console.log(`   🔍 开始解析弹幕数据...`);
                    const danmuList = parseDanmuData(result.data);
                    if (danmuList.length > 0) {
                        console.log(`   ✅ ${lineInfo.name} 解析成功: ${danmuList.length} 条弹幕`);
                        console.log(`   ${'─'.repeat(30)}`);
                        return { danmuList, lineName: lineInfo.name };
                    } else {
                        console.log(`   ⚠️  ${lineInfo.name} 解析失败: 数据格式不匹配或内容被过滤`);
                        console.log(`   ${'─'.repeat(30)}`);
                    }
                } else {
                    console.log(`   ❌ 返回空数据`);
                    console.log(`   ${'─'.repeat(30)}`);
                }
            } catch (error) {
                console.log(`   ❌ 请求异常: ${error.message}`);
                console.log(`   ${'─'.repeat(30)}`);
            }
            return null;
        });

        // 6. 处理弹幕解析结果
        console.log('\n📊 步骤6: 处理弹幕解析结果');
        let bestResult = null;

        if (useFastMode) {
            console.log('⚡ 快速模式: 等待第一个有效结果');
            try {
                bestResult = await Promise.any(
                    promises.map(async (promise) => {
                        const result = await promise;
                        if (result && result.danmuList && result.danmuList.length > 0) {
                            return result;
                        }
                        throw new Error('无效结果');
                    })
                );
            } catch (aggregateError) {
                const results = await Promise.allSettled(promises);
                const validResults = results
                    .filter(result => result.status === 'fulfilled' && result.value && result.value.danmuList && result.value.danmuList.length > 0)
                    .map(result => result.value);

                bestResult = validResults.length > 0 ? validResults[0] : null;
            }
        } else {
            console.log('🧠 智能模式: 等待所有结果并选择最佳');
            const results = await Promise.allSettled(promises);

            // 处理结果
            const validResults = [];
            results.forEach((result) => {
                if (result.status === 'fulfilled' && result.value && result.value.danmuList && result.value.danmuList.length > 0) {
                    validResults.push(result.value);
                }
            });

            const sortedResults = validResults.sort((a, b) => b.danmuList.length - a.danmuList.length);

            console.log('📈 各线路弹幕数量统计:');
            sortedResults.forEach(result => {
                console.log(`   ${result.lineName}: ${result.danmuList.length} 条`);
            });

            bestResult = sortedResults.length > 0 ? sortedResults[0] : null;
        }

        // 7. 返回最终结果
        console.log('\n🎉 步骤7: 生成最终结果');
        if (bestResult && bestResult.danmuList) {
            backData.data = bestResult.danmuList;

            const danmuSource = (selectedLineName === '智能' || selectedLineName === '快速') ? bestResult.lineName : selectedLineName;
            console.log(`✅ 弹幕解析成功!`);
            console.log(`   🏆 最佳线路: ${danmuSource}`);
            console.log(`   📊 弹幕数量: ${bestResult.danmuList.length} 条`);

            // 显示前5条弹幕示例
            console.log('   📝 弹幕示例:');
            bestResult.danmuList.slice(0, 5).forEach((danmu, index) => {
                console.log(`      ${index + 1}. [${danmu.time.toFixed(1)}s] ${danmu.content}`);
            });

        } else {
            console.log('❌ 所有弹幕线路都未返回有效数据');
            backData.data = [];
            backData.error = '未找到弹幕';
        }

    } catch (error) {
        console.log('💥 弹幕搜索过程中发生异常:', error.message);
        backData.error = error.toString();
    }

    if (backData.data.length === 0) {
        backData.error = backData.error || '未找到弹幕';
    }

    return backData;
}

// 创建readline接口
function createReadlineInterface() {
    return readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
}

// 获取用户输入的辅助函数
function getUserInput(rl, question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 交互式获取搜索参数
async function getSearchParametersFromUser() {
    const rl = createReadlineInterface();
    const args = new SearchParameters();

    console.log('\n📝 请输入搜索参数:');
    console.log('=' .repeat(40));

    // 获取视频名称
    args.name = await getUserInput(rl, '🎬 视频名称: ');
    if (!args.name) {
        console.log('❌ 视频名称不能为空');
        rl.close();
        return null;
    }

    // 获取集数
    const episodeInput = await getUserInput(rl, '📺 集数: ');
    if (!episodeInput) {
        console.log('❌ 集数不能为空');
        rl.close();
        return null;
    }
    args.episode = episodeInput;

    // 获取视频URL（可选）
    args.videoUrl = await getUserInput(rl, '🔗 视频URL (可选，直接回车跳过): ');

    // 获取弹幕线路
    console.log('\n🛣️  可选弹幕线路:');
    console.log('1. 智能 (推荐)');
    console.log('2. 快速');
    console.log('3. 虾米');
    console.log('4. DouFun');
    console.log('5. 弹幕库');
    console.log('6. 678');

    const lineChoice = await getUserInput(rl, '选择线路 (1-6，默认为1): ');
    const lineMap = {
        '1': '智能',
        '2': '快速',
        '3': '虾米',
        '4': 'DouFun',
        '5': '弹幕库',
        '6': '678'
    };
    args.line = lineMap[lineChoice] || '智能';

    rl.close();
    return args;
}

// 预设测试用例
const testCases = [
    {
        name: '测试案例1: 热门动漫',
        args: {
            name: '鬼灭之刃',
            episode: '1',
            line: '智能'
        }
    },
    {
        name: '测试案例2: 电视剧',
        args: {
            name: '庆余年',
            episode: '1',
            line: '快速'
        }
    },
    {
        name: '测试案例3: 指定线路',
        args: {
            name: '斗罗大陆',
            episode: '1',
            line: '虾米'
        }
    },
    {
        name: '测试案例4: 直接视频URL',
        args: {
            name: '测试视频',
            episode: '1',
            videoUrl: 'https://example.com/video.mp4',
            line: '智能'
        }
    }
];

// 运行单个测试
async function runSingleTest(testName, args) {
    console.log(`\n🔬 ${testName}`);
    console.log('=' .repeat(50));

    const startTime = Date.now();
    const result = await searchDanMu(args);
    const endTime = Date.now();

    console.log(`\n⏱️  执行时间: ${endTime - startTime}ms`);
    console.log('📋 最终结果:');
    console.log(`   状态: ${result.error ? '❌ 失败' : '✅ 成功'}`);
    console.log(`   弹幕数量: ${result.data.length} 条`);
    if (result.error) {
        console.log(`   错误信息: ${result.error}`);
    }

    console.log('\n' + '=' .repeat(50));
    return result;
}

// 运行预设测试用例
async function runPresetTests() {
    console.log('🧪 DanMu4.js 预设测试用例');
    console.log('=' .repeat(80));

    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        await runSingleTest(testCase.name, testCase.args);

        // 测试间隔，避免请求过于频繁
        if (i < testCases.length - 1) {
            console.log('⏳ 等待2秒后进行下一个测试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    console.log('\n🎊 所有预设测试完成!');
}

// 主菜单
async function showMainMenu() {
    const rl = createReadlineInterface();

    console.log('\n🎯 DanMu4.js 测试工具');
    console.log('=' .repeat(40));
    console.log('1. 手动输入参数测试');
    console.log('2. 运行预设测试用例');
    console.log('3. 退出');
    console.log('=' .repeat(40));

    const choice = await getUserInput(rl, '请选择操作 (1-3): ');
    rl.close();

    switch (choice) {
        case '1':
            // 手动输入参数
            const userArgs = await getSearchParametersFromUser();
            if (userArgs) {
                await runSingleTest('手动输入测试', userArgs);

                // 询问是否继续
                const continueRl = createReadlineInterface();
                const continueChoice = await getUserInput(continueRl, '\n是否继续测试? (y/n): ');
                continueRl.close();

                if (continueChoice.toLowerCase() === 'y' || continueChoice.toLowerCase() === 'yes') {
                    await showMainMenu();
                }
            }
            break;

        case '2':
            // 运行预设测试
            await runPresetTests();

            // 询问是否继续
            const continueRl2 = createReadlineInterface();
            const continueChoice2 = await getUserInput(continueRl2, '\n是否返回主菜单? (y/n): ');
            continueRl2.close();

            if (continueChoice2.toLowerCase() === 'y' || continueChoice2.toLowerCase() === 'yes') {
                await showMainMenu();
            }
            break;

        case '3':
            console.log('👋 再见!');
            break;

        default:
            console.log('❌ 无效选择，请重新选择');
            await showMainMenu();
            break;
    }
}

// 检查依赖
function checkDependencies() {
    try {
        require('axios');
        require('cheerio');
        return true;
    } catch (error) {
        console.log('❌ 缺少依赖包，请先安装:');
        console.log('npm install axios cheerio');
        return false;
    }
}

// 程序入口
if (require.main === module) {
    if (checkDependencies()) {
        showMainMenu().catch(error => {
            console.error('💥 程序执行失败:', error);
            process.exit(1);
        });
    } else {
        process.exit(1);
    }
}

module.exports = {
    searchDanMu,
    getVideoUrl,
    parseDanmuData,
    filterDanmuContent,
    DanMu,
    BackData,
    SearchParameters
};
