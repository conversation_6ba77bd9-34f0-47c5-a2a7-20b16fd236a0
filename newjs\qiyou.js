// ignore

//@name:奇游
//@webSite:http://www.qiyoudy4.com
//@version:1
//@remark:
//@codeID:
//@order: A

// ignore


/// 是否模拟 PC 是 1， 手机是 0
const isUsePC = 1
/// 默认应该是 0，当视频不能播放的时候，可以把这个设置为 1， 否则不要改动
const isAddReferer = 1

// 网站主页
const webSite = 'http://www.qiyoudy4.com'
// 网站搜索
// http://www.qiyoudy4.com/search.php
// 把网站主页变成 @{webSite} 把搜索词变成 @{searchWord}  把页码变成 @{page}
const searchUrl = '@{webSite}/search/@{searchWord}-------------.html'
// 当前网站任意视频详情页
// http://www.qiyoudy4.com/view/38286.html
const videoDetailPage = '@{webSite}/view/38286.html'
// 当前网站任意视频播放页
//http://www.dyxz3.com/play/38286-0-0.html
const videoPlayPage = '@{webSite}/play/38286-0-0.html'

// 保持不变
const filterListUrl = ''

const firstClass = [
    {
        name: '电影',
        // http://www.qiyoudy4.com/list/1.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/list/1.html',
    },
    {
        name: '电视剧',
        // http://www.qiyoudy4.com/list/2.html
        // 把网站主页变成 @{webSite}  把页码变成 @{page}
        id: '@{webSite}/list/2.html',
    },
]

// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要
// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要
// 下面这个不要有任何改动，且保持在最后一行，加载内置代码需要

//#BaseCode1#